name: Security Audit

on:
  schedule:
    - cron: "0 0 * * *" # Run daily at midnight UTC
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  audit:
    name: Security Audit
    runs-on: ubuntu-latest
    steps:
      - name: Checkout sources
        uses: actions/checkout@v4

      - name: Install stable toolchain
        uses: dtolnay/rust-toolchain@stable

      - name: Cache dependencies
        uses: Swatinem/rust-cache@v2

      - name: Install cargo-audit
        uses: taiki-e/install-action@cargo-audit

      - name: Run cargo audit
        run: cargo audit

  deny:
    name: Cargo Deny
    runs-on: ubuntu-latest
    steps:
      - name: Checkout sources
        uses: actions/checkout@v4

      - name: Install cargo-deny
        uses: taiki-e/install-action@cargo-deny

      - name: Run cargo deny
        run: cargo deny check
