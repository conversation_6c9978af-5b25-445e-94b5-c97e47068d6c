name: Release

# on:
#   push:
#     tags:
#       - "v*"
on:
  workflow_dispatch:

permissions:
  contents: write
  packages: write

env:
  CARGO_TERM_COLOR: always

jobs:
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout sources
        uses: actions/checkout@v4

      - name: Create Release
        id: create_release
        uses: softprops/action-gh-release@v2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          name: "rudu ${{ github.ref_name }}"
          draft: false
          prerelease: false
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  build-release:
    name: Build Release
    needs: create-release
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            target: x86_64-unknown-linux-gnu
            name: rudu-linux-x86_64
          - os: ubuntu-latest
            target: x86_64-unknown-linux-musl
            name: rudu-linux-x86_64-musl
          - os: windows-latest
            target: x86_64-pc-windows-msvc
            name: rudu-windows-x86_64.exe
          - os: macos-latest
            target: x86_64-apple-darwin
            name: rudu-macos-x86_64
          - os: macos-latest
            target: aarch64-apple-darwin
            name: rudu-macos-aarch64

    steps:
      - name: Checkout sources
        uses: actions/checkout@v4

      - name: Install toolchain
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}

      - name: Cache dependencies
        uses: Swatinem/rust-cache@v2

      - name: Install musl tools
        if: matrix.target == 'x86_64-unknown-linux-musl'
        run: sudo apt-get update && sudo apt-get install -y musl-tools

      - name: Build release binary
        run: cargo build --release --target ${{ matrix.target }}

      - name: Strip binary (Unix)
        if: matrix.os != 'windows-latest'
        run: strip target/${{ matrix.target }}/release/rudu

      - name: Create archive (Unix)
        if: matrix.os != 'windows-latest'
        run: |
          cd target/${{ matrix.target }}/release
          tar czf ../../../${{ matrix.name }}.tar.gz rudu
          cd -

      - name: Create archive (Windows)
        if: matrix.os == 'windows-latest'
        run: |
          cd target/${{ matrix.target }}/release
          7z a ../../../${{ matrix.name }}.zip rudu.exe
          cd -

      - name: Upload Release Asset (Unix)
        if: matrix.os != 'windows-latest'
        uses: softprops/action-gh-release@v2
        with:
          files: ./${{ matrix.name }}.tar.gz
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload Release Asset (Windows)
        if: matrix.os == 'windows-latest'
        uses: softprops/action-gh-release@v2
        with:
          files: ./${{ matrix.name }}.zip
          token: ${{ secrets.GITHUB_TOKEN }}

  publish-crates:
    name: Publish to Crates.io
    needs: build-release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout sources
        uses: actions/checkout@v4

      - name: Install stable toolchain
        uses: dtolnay/rust-toolchain@stable

      - name: Cache dependencies
        uses: Swatinem/rust-cache@v2

      - name: Publish to crates.io
        run: cargo publish --token ${{ secrets.CRATES_TOKEN }}
