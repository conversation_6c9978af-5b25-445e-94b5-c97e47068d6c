{"version": "0.2.1", "description": "Fast, parallel Rust CLI tool for analyzing directory sizes", "homepage": "https://github.com/ayungavis/rudu", "license": "MIT", "architecture": {"64bit": {"url": "https://github.com/ayungavis/rudu/releases/download/v0.2.1/rudu-windows-x86_64.zip", "hash": "sha256:5b8b579ef55d079bc3a93e2f73e70b2aeae8eff7254da78ba1b00698a9aa898f", "extract_dir": "rudu-windows-x86_64"}}, "bin": "rudu.exe", "checkver": {"github": "https://github.com/ayungavis/rudu"}, "autoupdate": {"architecture": {"64bit": {"url": "https://github.com/ayungavis/rudu/releases/download/v0.2.1/rudu-windows-x86_64.zip", "extract_dir": "rudu-windows-x86_64"}}}}